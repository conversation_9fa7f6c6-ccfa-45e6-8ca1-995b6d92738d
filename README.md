# نظام أرشفة المستندات الإلكترونية

نظام شامل لأرشفة وإدارة المستندات الإلكترونية باستخدام Python و tkinter و SQLite.

## الميزات

### 🔐 نظام تسجيل الدخول
- تسجيل دخول آمن بكلمة مرور مشفرة
- مستخدم افتراضي: `admin` / `admin123`

### 📄 إدارة المستندات
- إضافة مستندات جديدة مع معلومات شاملة
- دعم ملفات PDF والصور
- تصنيف المستندات (إداري، مالي، قانوني، إلخ)
- حفظ آمن للملفات في مجلد منفصل

### 🔍 البحث والتصفية
- البحث بالعنوان أو الوصف
- تصفية حسب التصنيف
- عرض تفاصيل المستندات

### ✏️ التعديل والحذف
- تعديل معلومات المستندات
- حذف المستندات مع تأكيد
- فتح المستندات مباشرة

### 📊 التقارير والإحصائيات
- إحصائيات حسب التصنيف
- إحصائيات حسب التاريخ
- رسوم بيانية تفاعلية
- تقارير مفصلة

## المتطلبات

### المتطلبات الأساسية
- Python 3.6 أو أحدث
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

### المتطلبات الاختيارية
```bash
pip install matplotlib
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd document-archive-system
```

### 2. تثبيت المتطلبات الاختيارية
```bash
pip install matplotlib
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## هيكل المشروع

```
document-archive-system/
├── main.py                 # الملف الرئيسي
├── config.py              # إعدادات البرنامج
├── database.py            # إدارة قاعدة البيانات
├── document_manager.py    # إدارة الملفات
├── gui/                   # واجهات المستخدم
│   ├── __init__.py
│   ├── login_window.py    # نافذة تسجيل الدخول
│   ├── main_window.py     # النافذة الرئيسية
│   ├── add_document.py    # نافذة إضافة مستند
│   ├── view_documents.py  # نافذة عرض المستندات
│   └── reports.py         # نافذة التقارير
├── documents/             # مجلد حفظ الملفات
├── documents_archive.db   # قاعدة البيانات (يتم إنشاؤها تلقائياً)
└── README.md             # هذا الملف
```

## كيفية الاستخدام

### 1. تسجيل الدخول
- استخدم المستخدم الافتراضي: `admin` / `admin123`
- أو قم بإنشاء مستخدم جديد في قاعدة البيانات

### 2. إضافة مستند جديد
1. اضغط على "إضافة مستند جديد"
2. أدخل عنوان المستند
3. أضف وصفاً (اختياري)
4. اختر التصنيف
5. اختر الملف (PDF أو صورة)
6. اضغط "حفظ المستند"

### 3. عرض المستندات
1. اضغط على "عرض المستندات"
2. استخدم البحث للعثور على مستندات محددة
3. اضغط مرتين على مستند لفتحه
4. استخدم أزرار التعديل والحذف حسب الحاجة

### 4. عرض التقارير
1. اضغط على "التقارير والإحصائيات"
2. اطلع على الإحصائيات النصية
3. تصفح الرسوم البيانية في التبويبات

## التخصيص

### إضافة تصنيفات جديدة
عدّل ملف `config.py`:
```python
DOCUMENT_CATEGORIES = [
    "إداري",
    "مالي", 
    "قانوني",
    "تقني",
    "شخصي",
    "التصنيف الجديد",  # أضف هنا
    "أخرى"
]
```

### تغيير أنواع الملفات المدعومة
عدّل ملف `config.py`:
```python
SUPPORTED_FILE_TYPES = [
    ("PDF files", "*.pdf"),
    ("Image files", "*.png;*.jpg;*.jpeg;*.gif;*.bmp"),
    ("Word files", "*.doc;*.docx"),  # أضف هنا
    ("All files", "*.*")
]
```

## قاعدة البيانات

### جدول المستخدمين (users)
- `id`: المعرف الفريد
- `username`: اسم المستخدم
- `password_hash`: كلمة المرور المشفرة
- `created_at`: تاريخ الإنشاء

### جدول المستندات (documents)
- `id`: المعرف الفريد
- `title`: عنوان المستند
- `description`: وصف المستند
- `category`: تصنيف المستند
- `file_path`: مسار الملف
- `file_name`: اسم الملف الأصلي
- `created_at`: تاريخ الإضافة
- `updated_at`: تاريخ آخر تحديث
- `user_id`: معرف المستخدم

## الأمان

- كلمات المرور مشفرة باستخدام SHA-256
- الملفات محفوظة في مجلد منفصل ومحمي
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection

## استكشاف الأخطاء

### مشكلة: لا تظهر الرسوم البيانية
**الحل**: تأكد من تثبيت matplotlib
```bash
pip install matplotlib
```

### مشكلة: خطأ في فتح الملفات
**الحل**: تأكد من وجود الملفات في مجلد `documents/`

### مشكلة: خطأ في قاعدة البيانات
**الحل**: احذف ملف `documents_archive.db` وأعد تشغيل البرنامج

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.
