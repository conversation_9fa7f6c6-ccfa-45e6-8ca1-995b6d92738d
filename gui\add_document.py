# -*- coding: utf-8 -*-
"""
واجهة إضافة مستند جديد
Add document window for the document archiving system
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import config

class AddDocumentWindow:
    def __init__(self, parent, db_manager, doc_manager, user_id, on_success=None):
        """تهيئة نافذة إضافة مستند"""
        self.parent = parent
        self.db_manager = db_manager
        self.doc_manager = doc_manager
        self.user_id = user_id
        self.on_success = on_success
        self.selected_file_path = None
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("إضافة مستند جديد")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_widgets()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # عنوان المستند
        ttk.Label(main_frame, text="عنوان المستند:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.title_var = tk.StringVar()
        self.title_entry = ttk.Entry(main_frame, textvariable=self.title_var, width=40)
        self.title_entry.grid(row=0, column=1, pady=5, padx=(10, 0), sticky=(tk.W, tk.E))
        
        # وصف المستند
        ttk.Label(main_frame, text="الوصف:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=5)
        self.description_text = tk.Text(main_frame, width=40, height=4)
        self.description_text.grid(row=1, column=1, pady=5, padx=(10, 0), sticky=(tk.W, tk.E))
        
        # شريط تمرير للوصف
        desc_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.description_text.yview)
        desc_scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S), pady=5)
        self.description_text.configure(yscrollcommand=desc_scrollbar.set)
        
        # التصنيف
        ttk.Label(main_frame, text="التصنيف:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(main_frame, textvariable=self.category_var, 
                                          values=config.DOCUMENT_CATEGORIES, 
                                          state="readonly", width=37)
        self.category_combo.grid(row=2, column=1, pady=5, padx=(10, 0), sticky=(tk.W, tk.E))
        self.category_combo.set(config.DOCUMENT_CATEGORIES[0])
        
        # اختيار الملف
        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=3, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E))
        
        ttk.Label(file_frame, text="الملف:").grid(row=0, column=0, sticky=tk.W)
        
        self.file_path_var = tk.StringVar(value="لم يتم اختيار ملف")
        self.file_path_label = ttk.Label(file_frame, textvariable=self.file_path_var, 
                                        foreground="gray", width=35)
        self.file_path_label.grid(row=0, column=1, padx=10, sticky=(tk.W, tk.E))
        
        self.browse_button = ttk.Button(file_frame, text="اختيار ملف", 
                                       command=self.browse_file)
        self.browse_button.grid(row=0, column=2)
        
        # معلومات الملف
        self.file_info_var = tk.StringVar()
        self.file_info_label = ttk.Label(file_frame, textvariable=self.file_info_var, 
                                        foreground="blue", font=('Arial', 8))
        self.file_info_label.grid(row=1, column=1, columnspan=2, sticky=tk.W, pady=5)
        
        # أزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)
        
        self.save_button = ttk.Button(button_frame, text="حفظ المستند", 
                                     command=self.save_document)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        self.cancel_button = ttk.Button(button_frame, text="إلغاء", 
                                       command=self.window.destroy)
        self.cancel_button.pack(side=tk.LEFT, padx=5)
        
        # تركيز على حقل العنوان
        self.title_entry.focus()
        
        # تكوين الأعمدة للتمدد
        main_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(1, weight=1)
    
    def browse_file(self):
        """اختيار ملف"""
        file_path = filedialog.askopenfilename(
            title="اختيار ملف المستند",
            filetypes=config.SUPPORTED_FILE_TYPES
        )
        
        if file_path:
            self.selected_file_path = file_path
            filename = os.path.basename(file_path)
            self.file_path_var.set(filename)
            self.file_path_label.configure(foreground="black")
            
            # عرض معلومات الملف
            try:
                file_size = os.path.getsize(file_path)
                formatted_size = self.doc_manager.format_file_size(file_size)
                self.file_info_var.set(f"حجم الملف: {formatted_size}")
            except:
                self.file_info_var.set("")
    
    def save_document(self):
        """حفظ المستند"""
        # التحقق من البيانات
        title = self.title_var.get().strip()
        description = self.description_text.get("1.0", tk.END).strip()
        category = self.category_var.get()
        
        if not title:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان المستند")
            self.title_entry.focus()
            return
        
        if not category:
            messagebox.showerror("خطأ", "يرجى اختيار تصنيف المستند")
            return
        
        if not self.selected_file_path:
            messagebox.showerror("خطأ", "يرجى اختيار ملف المستند")
            return
        
        try:
            # إضافة المستند إلى قاعدة البيانات أولاً للحصول على ID
            original_filename = os.path.basename(self.selected_file_path)
            doc_id = self.db_manager.add_document(
                title, description, category, "", original_filename, self.user_id
            )
            
            # حفظ الملف
            file_path, saved_filename = self.doc_manager.save_document_file(
                self.selected_file_path, doc_id, original_filename
            )
            
            # تحديث مسار الملف في قاعدة البيانات
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE documents SET file_path = ? WHERE id = ?",
                (file_path, doc_id)
            )
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", "تم حفظ المستند بنجاح")
            
            # استدعاء دالة النجاح إذا كانت موجودة
            if self.on_success:
                self.on_success()
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ المستند:\n{str(e)}")
