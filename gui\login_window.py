# -*- coding: utf-8 -*-
"""
واجهة تسجيل الدخول
Login window for the document archiving system
"""

import tkinter as tk
from tkinter import ttk, messagebox
import config

class LoginWindow:
    def __init__(self, db_manager, on_login_success):
        """تهيئة نافذة تسجيل الدخول"""
        self.db_manager = db_manager
        self.on_login_success = on_login_success
        self.user_id = None
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("تسجيل الدخول - " + config.WINDOW_TITLE)
        self.root.geometry(config.LOGIN_WINDOW_SIZE)
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda event: self.login())
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # عنوان
        title_label = ttk.Label(main_frame, text="نظام أرشفة المستندات", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # اسم المستخدم
        ttk.Label(main_frame, text="اسم المستخدم:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar(value="admin")
        self.username_entry = ttk.Entry(main_frame, textvariable=self.username_var, width=25)
        self.username_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar(value="admin123")
        self.password_entry = ttk.Entry(main_frame, textvariable=self.password_var, 
                                       show="*", width=25)
        self.password_entry.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # أزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        self.login_button = ttk.Button(button_frame, text="تسجيل الدخول", 
                                      command=self.login)
        self.login_button.pack(side=tk.LEFT, padx=5)
        
        self.cancel_button = ttk.Button(button_frame, text="إلغاء", 
                                       command=self.root.quit)
        self.cancel_button.pack(side=tk.LEFT, padx=5)
        
        # رسالة المساعدة
        help_label = ttk.Label(main_frame, 
                              text="المستخدم الافتراضي: admin\nكلمة المرور: admin123",
                              font=('Arial', 8), foreground='gray')
        help_label.grid(row=4, column=0, columnspan=2, pady=10)
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # التحقق من بيانات المستخدم
        user_id = self.db_manager.verify_user(username, password)
        
        if user_id:
            self.user_id = user_id
            self.root.destroy()
            self.on_login_success(user_id)
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_var.set("")
            self.password_entry.focus()
    
    def show(self):
        """عرض نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.user_id
