# -*- coding: utf-8 -*-
"""
واجهة التقارير
Reports window for the document archiving system
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
matplotlib.use('TkAgg')

class ReportsWindow:
    def __init__(self, parent, db_manager):
        """تهيئة نافذة التقارير"""
        self.parent = parent
        self.db_manager = db_manager
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("التقارير والإحصائيات")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات
        self.load_statistics()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان
        title_label = ttk.Label(main_frame, text="التقارير والإحصائيات", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار الإحصائيات النصية
        stats_frame = ttk.LabelFrame(main_frame, text="الإحصائيات العامة", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.stats_text = tk.Text(stats_frame, height=8, width=80, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.X)
        
        # إطار الرسوم البيانية
        charts_frame = ttk.LabelFrame(main_frame, text="الرسوم البيانية", padding="10")
        charts_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(charts_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب التصنيفات
        self.category_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.category_frame, text="حسب التصنيف")
        
        # تبويب التواريخ
        self.date_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.date_frame, text="حسب التاريخ")
        
        # إطار الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="تحديث", 
                  command=self.load_statistics).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="إغلاق", 
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # الحصول على الإحصائيات من قاعدة البيانات
            stats = self.db_manager.get_document_stats()
            
            # عرض الإحصائيات النصية
            self.display_text_statistics(stats)
            
            # عرض الرسوم البيانية
            self.display_category_chart(stats['category_stats'])
            self.display_date_chart(stats['date_stats'])
            
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {str(e)}")
    
    def display_text_statistics(self, stats):
        """عرض الإحصائيات النصية"""
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        
        # العدد الإجمالي
        self.stats_text.insert(tk.END, f"العدد الإجمالي للمستندات: {stats['total_count']}\n\n")
        
        # إحصائيات التصنيفات
        self.stats_text.insert(tk.END, "توزيع المستندات حسب التصنيف:\n")
        self.stats_text.insert(tk.END, "-" * 40 + "\n")
        
        for category, count in stats['category_stats']:
            percentage = (count / stats['total_count'] * 100) if stats['total_count'] > 0 else 0
            self.stats_text.insert(tk.END, f"{category}: {count} مستند ({percentage:.1f}%)\n")
        
        # إحصائيات التواريخ (آخر 10 أيام)
        if stats['date_stats']:
            self.stats_text.insert(tk.END, f"\nالمستندات المضافة في آخر {len(stats['date_stats'])} أيام:\n")
            self.stats_text.insert(tk.END, "-" * 40 + "\n")
            
            for date, count in stats['date_stats'][:10]:  # أول 10 أيام
                self.stats_text.insert(tk.END, f"{date}: {count} مستند\n")
        
        self.stats_text.config(state=tk.DISABLED)
    
    def display_category_chart(self, category_stats):
        """عرض رسم بياني للتصنيفات"""
        # مسح الرسم السابق
        for widget in self.category_frame.winfo_children():
            widget.destroy()
        
        if not category_stats:
            ttk.Label(self.category_frame, text="لا توجد بيانات لعرضها").pack(expand=True)
            return
        
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(8, 5))
        
        categories = [item[0] for item in category_stats]
        counts = [item[1] for item in category_stats]
        
        # رسم بياني دائري
        ax.pie(counts, labels=categories, autopct='%1.1f%%', startangle=90)
        ax.set_title('توزيع المستندات حسب التصنيف')
        
        # إضافة الرسم إلى النافذة
        canvas = FigureCanvasTkAgg(fig, self.category_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def display_date_chart(self, date_stats):
        """عرض رسم بياني للتواريخ"""
        # مسح الرسم السابق
        for widget in self.date_frame.winfo_children():
            widget.destroy()
        
        if not date_stats:
            ttk.Label(self.date_frame, text="لا توجد بيانات لعرضها").pack(expand=True)
            return
        
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(10, 5))
        
        dates = [item[0] for item in date_stats]
        counts = [item[1] for item in date_stats]
        
        # رسم بياني خطي
        ax.plot(dates, counts, marker='o', linestyle='-', linewidth=2, markersize=6)
        ax.set_title('المستندات المضافة خلال آخر 30 يوم')
        ax.set_xlabel('التاريخ')
        ax.set_ylabel('عدد المستندات')
        ax.grid(True, alpha=0.3)
        
        # تدوير تسميات التاريخ
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إضافة الرسم إلى النافذة
        canvas = FigureCanvasTkAgg(fig, self.date_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
