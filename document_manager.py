# -*- coding: utf-8 -*-
"""
إدارة المستندات
Document management utilities
"""

import os
import shutil
from datetime import datetime
import config

class DocumentManager:
    def __init__(self):
        """تهيئة مدير المستندات"""
        self.documents_folder = config.DOCUMENTS_FOLDER
        
    def save_document_file(self, source_path, document_id, original_filename):
        """حفظ ملف المستند في مجلد المشروع"""
        try:
            # إنشاء اسم ملف فريد
            file_extension = os.path.splitext(original_filename)[1]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_filename = f"doc_{document_id}_{timestamp}{file_extension}"
            
            # المسار الكامل للملف الجديد
            destination_path = os.path.join(self.documents_folder, new_filename)
            
            # نسخ الملف
            shutil.copy2(source_path, destination_path)
            
            return destination_path, new_filename
            
        except Exception as e:
            raise Exception(f"خطأ في حفظ الملف: {str(e)}")
    
    def delete_document_file(self, file_path):
        """حذف ملف المستند"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف الملف: {str(e)}")
            return False
    
    def open_document_file(self, file_path):
        """فتح ملف المستند"""
        try:
            if os.path.exists(file_path):
                os.startfile(file_path)  # Windows
                return True
            else:
                raise Exception("الملف غير موجود")
        except Exception as e:
            raise Exception(f"خطأ في فتح الملف: {str(e)}")
    
    def get_file_info(self, file_path):
        """الحصول على معلومات الملف"""
        try:
            if os.path.exists(file_path):
                stat = os.stat(file_path)
                return {
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'exists': True
                }
            else:
                return {'exists': False}
        except Exception as e:
            return {'exists': False, 'error': str(e)}
    
    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
