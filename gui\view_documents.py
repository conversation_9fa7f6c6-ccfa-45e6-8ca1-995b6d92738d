# -*- coding: utf-8 -*-
"""
واجهة عرض المستندات
View documents window for the document archiving system
"""

import tkinter as tk
from tkinter import ttk, messagebox
import config

class ViewDocumentsWindow:
    def __init__(self, parent, db_manager, doc_manager, user_id):
        """تهيئة نافذة عرض المستندات"""
        self.parent = parent
        self.db_manager = db_manager
        self.doc_manager = doc_manager
        self.user_id = user_id
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("عرض المستندات")
        self.window.geometry("900x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل المستندات
        self.load_documents()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار البحث
        search_frame = ttk.Frame(self.window, padding="10")
        search_frame.pack(fill=tk.X)
        
        # البحث بالعنوان
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        
        # البحث بالتصنيف
        ttk.Label(search_frame, text="التصنيف:").pack(side=tk.LEFT, padx=(20, 5))
        self.category_filter_var = tk.StringVar()
        category_values = ["جميع التصنيفات"] + config.DOCUMENT_CATEGORIES
        self.category_filter_combo = ttk.Combobox(search_frame, 
                                                 textvariable=self.category_filter_var,
                                                 values=category_values, 
                                                 state="readonly", width=15)
        self.category_filter_combo.pack(side=tk.LEFT, padx=5)
        self.category_filter_combo.set("جميع التصنيفات")
        
        # زر البحث
        self.search_button = ttk.Button(search_frame, text="بحث", 
                                       command=self.search_documents)
        self.search_button.pack(side=tk.LEFT, padx=5)
        
        # زر إعادة تحميل
        self.refresh_button = ttk.Button(search_frame, text="تحديث", 
                                        command=self.load_documents)
        self.refresh_button.pack(side=tk.LEFT, padx=5)
        
        # جدول المستندات
        table_frame = ttk.Frame(self.window, padding="10")
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ("ID", "العنوان", "التصنيف", "تاريخ الإضافة", "اسم الملف")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف الأعمدة
        self.tree.heading("ID", text="ID")
        self.tree.heading("العنوان", text="العنوان")
        self.tree.heading("التصنيف", text="التصنيف")
        self.tree.heading("تاريخ الإضافة", text="تاريخ الإضافة")
        self.tree.heading("اسم الملف", text="اسم الملف")
        
        # تحديد عرض الأعمدة
        self.tree.column("ID", width=50)
        self.tree.column("العنوان", width=200)
        self.tree.column("التصنيف", width=100)
        self.tree.column("تاريخ الإضافة", width=150)
        self.tree.column("اسم الملف", width=200)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب العناصر
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار الأزرار
        button_frame = ttk.Frame(self.window, padding="10")
        button_frame.pack(fill=tk.X)
        
        self.open_button = ttk.Button(button_frame, text="فتح المستند", 
                                     command=self.open_document)
        self.open_button.pack(side=tk.LEFT, padx=5)
        
        self.edit_button = ttk.Button(button_frame, text="تعديل", 
                                     command=self.edit_document)
        self.edit_button.pack(side=tk.LEFT, padx=5)
        
        self.delete_button = ttk.Button(button_frame, text="حذف", 
                                       command=self.delete_document)
        self.delete_button.pack(side=tk.LEFT, padx=5)
        
        self.close_button = ttk.Button(button_frame, text="إغلاق", 
                                      command=self.window.destroy)
        self.close_button.pack(side=tk.RIGHT, padx=5)
        
        # ربط الأحداث
        self.tree.bind("<Double-1>", lambda event: self.open_document())
        self.search_entry.bind("<Return>", lambda event: self.search_documents())
    
    def load_documents(self):
        """تحميل المستندات"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # الحصول على المستندات
        documents = self.db_manager.get_documents()
        
        # إضافة المستندات إلى الجدول
        for doc in documents:
            doc_id, title, description, category, file_path, file_name, created_at, updated_at, user_id = doc
            # تنسيق التاريخ
            formatted_date = created_at.split()[0] if created_at else ""
            
            self.tree.insert("", "end", values=(doc_id, title, category, formatted_date, file_name))
    
    def search_documents(self):
        """البحث في المستندات"""
        search_term = self.search_var.get().strip()
        category_filter = self.category_filter_var.get()
        
        # تحديد التصنيف للبحث
        category = None if category_filter == "جميع التصنيفات" else category_filter
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # البحث
        documents = self.db_manager.get_documents(
            search_term=search_term if search_term else None,
            category=category
        )
        
        # إضافة النتائج
        for doc in documents:
            doc_id, title, description, category, file_path, file_name, created_at, updated_at, user_id = doc
            formatted_date = created_at.split()[0] if created_at else ""
            
            self.tree.insert("", "end", values=(doc_id, title, category, formatted_date, file_name))
    
    def get_selected_document(self):
        """الحصول على المستند المحدد"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستند")
            return None
        
        item = self.tree.item(selection[0])
        doc_id = item['values'][0]
        
        # الحصول على تفاصيل المستند من قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM documents WHERE id = ?", (doc_id,))
        document = cursor.fetchone()
        conn.close()
        
        return document
    
    def open_document(self):
        """فتح المستند"""
        document = self.get_selected_document()
        if not document:
            return
        
        file_path = document[4]  # file_path هو العمود الخامس
        
        try:
            self.doc_manager.open_document_file(file_path)
        except Exception as e:
            messagebox.showerror("خطأ", str(e))
    
    def edit_document(self):
        """تعديل المستند"""
        document = self.get_selected_document()
        if not document:
            return
        
        # إنشاء نافذة التعديل
        EditDocumentWindow(self.window, self.db_manager, document, self.load_documents)
    
    def delete_document(self):
        """حذف المستند"""
        document = self.get_selected_document()
        if not document:
            return
        
        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف", 
                                    f"هل أنت متأكد من حذف المستند '{document[1]}'؟")
        
        if result:
            try:
                # حذف الملف
                file_path = self.db_manager.delete_document(document[0])
                if file_path:
                    self.doc_manager.delete_document_file(file_path)
                
                messagebox.showinfo("نجح", "تم حذف المستند بنجاح")
                self.load_documents()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المستند:\n{str(e)}")


class EditDocumentWindow:
    def __init__(self, parent, db_manager, document, on_success=None):
        """نافذة تعديل المستند"""
        self.parent = parent
        self.db_manager = db_manager
        self.document = document
        self.on_success = on_success
        
        # إنشاء النافذة
        self.window = tk.Toplevel(parent)
        self.window.title("تعديل المستند")
        self.window.geometry("500x300")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_widgets()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # العنوان
        ttk.Label(main_frame, text="عنوان المستند:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.title_var = tk.StringVar(value=self.document[1])
        self.title_entry = ttk.Entry(main_frame, textvariable=self.title_var, width=40)
        self.title_entry.grid(row=0, column=1, pady=5, padx=(10, 0))
        
        # الوصف
        ttk.Label(main_frame, text="الوصف:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=5)
        self.description_text = tk.Text(main_frame, width=40, height=4)
        self.description_text.grid(row=1, column=1, pady=5, padx=(10, 0))
        self.description_text.insert("1.0", self.document[2] or "")
        
        # التصنيف
        ttk.Label(main_frame, text="التصنيف:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.category_var = tk.StringVar(value=self.document[3])
        self.category_combo = ttk.Combobox(main_frame, textvariable=self.category_var,
                                          values=config.DOCUMENT_CATEGORIES,
                                          state="readonly", width=37)
        self.category_combo.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="حفظ التغييرات", 
                  command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="إلغاء", 
                  command=self.window.destroy).pack(side=tk.LEFT, padx=5)
    
    def save_changes(self):
        """حفظ التغييرات"""
        title = self.title_var.get().strip()
        description = self.description_text.get("1.0", tk.END).strip()
        category = self.category_var.get()
        
        if not title:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان المستند")
            return
        
        try:
            self.db_manager.update_document(self.document[0], title, description, category)
            messagebox.showinfo("نجح", "تم حفظ التغييرات بنجاح")
            
            if self.on_success:
                self.on_success()
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ التغييرات:\n{str(e)}")
