# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات
Database management for the document archiving system
"""

import sqlite3
import hashlib
from datetime import datetime
import config

class DatabaseManager:
    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        self.db_name = config.DATABASE_NAME
        self.init_database()
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إنشاء جدول المستندات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                category TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # إضافة مستخدم افتراضي إذا لم يكن موجوداً
        cursor.execute("SELECT COUNT(*) FROM users")
        if cursor.fetchone()[0] == 0:
            default_password = self.hash_password("admin123")
            cursor.execute(
                "INSERT INTO users (username, password_hash) VALUES (?, ?)",
                ("admin", default_password)
            )
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_user(self, username, password):
        """التحقق من بيانات المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute(
            "SELECT id FROM users WHERE username = ? AND password_hash = ?",
            (username, password_hash)
        )
        
        user = cursor.fetchone()
        conn.close()
        
        return user[0] if user else None
    
    def add_document(self, title, description, category, file_path, file_name, user_id):
        """إضافة مستند جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO documents (title, description, category, file_path, file_name, user_id)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (title, description, category, file_path, file_name, user_id))
        
        document_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return document_id
    
    def get_documents(self, search_term=None, category=None):
        """الحصول على قائمة المستندات مع إمكانية البحث"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM documents WHERE 1=1"
        params = []
        
        if search_term:
            query += " AND (title LIKE ? OR description LIKE ?)"
            params.extend([f"%{search_term}%", f"%{search_term}%"])
        
        if category:
            query += " AND category = ?"
            params.append(category)
        
        query += " ORDER BY created_at DESC"
        
        cursor.execute(query, params)
        documents = cursor.fetchall()
        conn.close()
        
        return documents
    
    def update_document(self, doc_id, title, description, category):
        """تحديث بيانات مستند"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE documents 
            SET title = ?, description = ?, category = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (title, description, category, doc_id))
        
        conn.commit()
        conn.close()
    
    def delete_document(self, doc_id):
        """حذف مستند"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # الحصول على مسار الملف قبل الحذف
        cursor.execute("SELECT file_path FROM documents WHERE id = ?", (doc_id,))
        result = cursor.fetchone()
        
        if result:
            cursor.execute("DELETE FROM documents WHERE id = ?", (doc_id,))
            conn.commit()
            conn.close()
            return result[0]  # إرجاع مسار الملف
        
        conn.close()
        return None
    
    def get_document_stats(self):
        """الحصول على إحصائيات المستندات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # إحصائيات حسب التصنيف
        cursor.execute('''
            SELECT category, COUNT(*) as count 
            FROM documents 
            GROUP BY category 
            ORDER BY count DESC
        ''')
        category_stats = cursor.fetchall()
        
        # إحصائيات حسب التاريخ (آخر 30 يوم)
        cursor.execute('''
            SELECT DATE(created_at) as date, COUNT(*) as count 
            FROM documents 
            WHERE created_at >= date('now', '-30 days')
            GROUP BY DATE(created_at) 
            ORDER BY date DESC
        ''')
        date_stats = cursor.fetchall()
        
        # العدد الإجمالي
        cursor.execute("SELECT COUNT(*) FROM documents")
        total_count = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'category_stats': category_stats,
            'date_stats': date_stats,
            'total_count': total_count
        }
