# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية
Main window for the document archiving system
"""

import tkinter as tk
from tkinter import ttk, messagebox
import config
from gui.add_document import AddDocumentWindow
from gui.view_documents import ViewDocumentsWindow
from gui.reports import ReportsWindow

class MainWindow:
    def __init__(self, db_manager, doc_manager, user_id):
        """تهيئة النافذة الرئيسية"""
        self.db_manager = db_manager
        self.doc_manager = doc_manager
        self.user_id = user_id
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title(config.WINDOW_TITLE)
        self.root.geometry(config.MAIN_WINDOW_SIZE)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل الإحصائيات الأولية
        self.update_statistics()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط القوائم
        self.create_menu()
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عنوان البرنامج
        title_label = ttk.Label(main_frame, text="نظام أرشفة المستندات الإلكترونية", 
                               font=('Arial', 20, 'bold'))
        title_label.pack(pady=(0, 30))
        
        # إطار الأزرار الرئيسية
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)
        
        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(buttons_frame)
        row1_frame.pack(pady=10)
        
        self.add_doc_button = ttk.Button(row1_frame, text="إضافة مستند جديد", 
                                        command=self.add_document,
                                        width=20)
        self.add_doc_button.pack(side=tk.LEFT, padx=10)
        
        self.view_docs_button = ttk.Button(row1_frame, text="عرض المستندات", 
                                          command=self.view_documents,
                                          width=20)
        self.view_docs_button.pack(side=tk.LEFT, padx=10)
        
        # الصف الثاني من الأزرار
        row2_frame = ttk.Frame(buttons_frame)
        row2_frame.pack(pady=10)
        
        self.reports_button = ttk.Button(row2_frame, text="التقارير والإحصائيات", 
                                        command=self.show_reports,
                                        width=20)
        self.reports_button.pack(side=tk.LEFT, padx=10)
        
        self.exit_button = ttk.Button(row2_frame, text="خروج", 
                                     command=self.exit_application,
                                     width=20)
        self.exit_button.pack(side=tk.LEFT, padx=10)
        
        # إطار الإحصائيات السريعة
        stats_frame = ttk.LabelFrame(main_frame, text="إحصائيات سريعة", padding="15")
        stats_frame.pack(fill=tk.X, pady=30)
        
        self.stats_text = tk.Text(stats_frame, height=8, state=tk.DISABLED, 
                                 font=('Arial', 10))
        self.stats_text.pack(fill=tk.X)
        
        # شريط الحالة
        self.status_bar = ttk.Label(self.root, text="مرحباً بك في نظام أرشفة المستندات", 
                                   relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إضافة مستند جديد", command=self.add_document)
        file_menu.add_command(label="عرض المستندات", command=self.view_documents)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_application)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="الإحصائيات", command=self.show_reports)
        reports_menu.add_command(label="تحديث الإحصائيات", command=self.update_statistics)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def add_document(self):
        """فتح نافذة إضافة مستند"""
        AddDocumentWindow(self.root, self.db_manager, self.doc_manager, 
                         self.user_id, self.update_statistics)
    
    def view_documents(self):
        """فتح نافذة عرض المستندات"""
        ViewDocumentsWindow(self.root, self.db_manager, self.doc_manager, self.user_id)
    
    def show_reports(self):
        """فتح نافذة التقارير"""
        try:
            ReportsWindow(self.root, self.db_manager)
        except ImportError:
            messagebox.showwarning("تحذير", 
                                 "مكتبة matplotlib غير مثبتة. يرجى تثبيتها لعرض الرسوم البيانية.\n"
                                 "pip install matplotlib")
    
    def update_statistics(self):
        """تحديث الإحصائيات السريعة"""
        try:
            stats = self.db_manager.get_document_stats()
            
            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete(1.0, tk.END)
            
            # العدد الإجمالي
            self.stats_text.insert(tk.END, f"العدد الإجمالي للمستندات: {stats['total_count']}\n\n")
            
            # أهم التصنيفات
            if stats['category_stats']:
                self.stats_text.insert(tk.END, "أهم التصنيفات:\n")
                for category, count in stats['category_stats'][:5]:  # أول 5 تصنيفات
                    self.stats_text.insert(tk.END, f"• {category}: {count} مستند\n")
            
            # آخر المستندات المضافة
            if stats['date_stats']:
                self.stats_text.insert(tk.END, f"\nآخر المستندات المضافة:\n")
                for date, count in stats['date_stats'][:3]:  # آخر 3 أيام
                    self.stats_text.insert(tk.END, f"• {date}: {count} مستند\n")
            
            self.stats_text.config(state=tk.DISABLED)
            
            # تحديث شريط الحالة
            self.status_bar.config(text=f"تم تحديث الإحصائيات - إجمالي المستندات: {stats['total_count']}")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
نظام أرشفة المستندات الإلكترونية

الإصدار: 1.0
تم التطوير باستخدام Python و tkinter

الميزات:
• إضافة وإدارة المستندات
• البحث والتصفية
• التقارير والإحصائيات
• واجهة مستخدم سهلة الاستخدام

المطور: نظام أرشفة المستندات
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def exit_application(self):
        """إغلاق البرنامج"""
        result = messagebox.askyesno("تأكيد الخروج", "هل أنت متأكد من إغلاق البرنامج؟")
        if result:
            self.root.quit()
    
    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()
