# -*- coding: utf-8 -*-
"""
الملف الرئيسي لنظام أرشفة المستندات الإلكترونية
Main file for the document archiving system

هذا البرنامج يوفر نظام أرشفة إلكترونية للمستندات مع الميزات التالية:
- تسجيل الدخول للمستخدمين
- إضافة وإدارة المستندات
- البحث والتصفية
- التقارير والإحصائيات
- واجهة مستخدم باللغة العربية

المتطلبات:
- Python 3.6+
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)
- matplotlib (للرسوم البيانية)

للتشغيل:
python main.py

المطور: نظام أرشفة المستندات
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات المطلوبة
try:
    from database import DatabaseManager
    from document_manager import DocumentManager
    from gui.login_window import LoginWindow
    from gui.main_window import MainWindow
    import config
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    sys.exit(1)

class DocumentArchiveApp:
    """الفئة الرئيسية لتطبيق أرشفة المستندات"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.db_manager = None
        self.doc_manager = None
        self.current_user_id = None
        
        # تهيئة المكونات
        self.initialize_components()
    
    def initialize_components(self):
        """تهيئة مكونات التطبيق"""
        try:
            # تهيئة مدير قاعدة البيانات
            self.db_manager = DatabaseManager()
            print("تم تهيئة قاعدة البيانات بنجاح")
            
            # تهيئة مدير المستندات
            self.doc_manager = DocumentManager()
            print("تم تهيئة مدير المستندات بنجاح")
            
        except Exception as e:
            print(f"خطأ في تهيئة المكونات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تهيئة البرنامج:\n{str(e)}")
            sys.exit(1)
    
    def on_login_success(self, user_id):
        """دالة يتم استدعاؤها عند نجاح تسجيل الدخول"""
        self.current_user_id = user_id
        print(f"تم تسجيل الدخول بنجاح للمستخدم ID: {user_id}")
        
        # فتح النافذة الرئيسية
        self.show_main_window()
    
    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            login_window = LoginWindow(self.db_manager, self.on_login_success)
            user_id = login_window.show()
            
            if user_id is None:
                # المستخدم ألغى تسجيل الدخول
                print("تم إلغاء تسجيل الدخول")
                return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في نافذة تسجيل الدخول: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في نافذة تسجيل الدخول:\n{str(e)}")
            return False
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            main_window = MainWindow(self.db_manager, self.doc_manager, self.current_user_id)
            main_window.run()
            
        except Exception as e:
            print(f"خطأ في النافذة الرئيسية: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في النافذة الرئيسية:\n{str(e)}")
    
    def run(self):
        """تشغيل التطبيق"""
        print("بدء تشغيل نظام أرشفة المستندات...")
        print("=" * 50)
        
        # عرض نافذة تسجيل الدخول
        if self.show_login_window():
            print("تم إغلاق البرنامج بنجاح")
        else:
            print("تم إنهاء البرنامج")

def check_requirements():
    """فحص المتطلبات المطلوبة"""
    print("فحص المتطلبات...")
    
    # فحص إصدار Python
    if sys.version_info < (3, 6):
        print("خطأ: يتطلب Python 3.6 أو أحدث")
        return False
    
    # فحص tkinter
    try:
        import tkinter
        print("✓ tkinter متوفر")
    except ImportError:
        print("خطأ: tkinter غير متوفر")
        return False
    
    # فحص sqlite3
    try:
        import sqlite3
        print("✓ sqlite3 متوفر")
    except ImportError:
        print("خطأ: sqlite3 غير متوفر")
        return False
    
    # فحص matplotlib (اختياري)
    try:
        import matplotlib
        print("✓ matplotlib متوفر")
    except ImportError:
        print("تحذير: matplotlib غير متوفر - لن تعمل الرسوم البيانية")
    
    print("تم فحص المتطلبات بنجاح")
    return True

def main():
    """الدالة الرئيسية"""
    print("نظام أرشفة المستندات الإلكترونية")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    try:
        # إنشاء وتشغيل التطبيق
        app = DocumentArchiveApp()
        app.run()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}")
    
    print("شكراً لاستخدام نظام أرشفة المستندات")

if __name__ == "__main__":
    main()
